# syntax=docker/dockerfile:1.7-labs

# STAGE: BASE
# Use Slim instead of Alpine for better compatibility
FROM node:22-slim AS base

# Set environment (DO NOT combine into a single ENV command)
ARG NODE_ENV
ENV COREPACK_ENABLE_AUTO_PIN=0
ENV COREPACK_INTEGRITY_KEYS=0
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=$NODE_ENV
ENV SHELL=/bin/bash

ENV CLARA_HOME="/opt/clara"
ENV CLARA_BIN="$CLARA_HOME/apps/cli/src/index.ts"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV GCLOUD_RW_API_KEY="****************************************************************************************************************************************************************"

# Install utils, deps, and configs we want in the final image
RUN apt-get -y update \
    && apt-get install -y \
        caddy \
        htop \
        imagemagick \
        graphicsmagick \
        jq \
        logrotate \
        lsof \
        nano \
        openssh-client \
        procps \
        file \
    && if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then \
            apt-get install -y \
               inotify-tools \
               iproute2 \
               rsync \
               ; \
            echo "/var/log/clara/*.log {\n    size 20M\n    compress\n    rotate 14\n    missingok\n    notifempty\n    create 0640 root root\n    sharedscripts\n}" > /etc/logrotate.d/clara; \
        else \
            echo "/var/log/clara/*.log {\n    size 50M\n    compress\n    rotate 28\n    missingok\n    notifempty\n    create 0640 root root\n    sharedscripts\n}" > /etc/logrotate.d/clara; \
        fi \
    && echo "#!/bin/sh\n/usr/sbin/logrotate /etc/logrotate.d/clara" > /etc/cron.hourly/logrotate \
    && mkdir -p $CLARA_HOME \
    && mkdir -p $PNPM_HOME \
    && corepack enable \
    && corepack prepare pnpm@latest --activate \
    && pnpm i --no-optional --cache-dir .pnpm-cache -g --prod \
        oclif \
        pm2 \
        typescript \
        tsx \
    && pnpm i --cache-dir .pnpm-cache -g --prod \
        esbuild \
    && rm -rf .pnpm-cache \
    && mkdir -p /var/log/clara \
    && echo "PATH=\"$PNPM_HOME:\$PATH\"" >> /etc/profile.d/root.sh \
    && echo "export PS1='\$(whoami)@\$(hostname):\$(pwd)# '" >> /etc/profile.d/root.sh \
    && echo 'export GCLOUD_TENANT_ID="$(echo ${FLY_ID} | cut -d"-" -f2)"' >> /etc/profile.d/root.sh \
    && echo 'export GCLOUD_ENVIRONMENT="$(prefix=$(echo ${FLY_ID} | cut -d"-" -f1); case "$prefix" in "t") echo "testing";; "s") echo "staging";; "p") echo "production";; *) echo "development";; esac)"' >> /etc/profile.d/root.sh \
    && echo 'export GCLOUD_SERVICE_GROUP="nes"' >> /etc/profile.d/root.sh \
    && echo 'export GCLOUD_PLATFORM="$(echo ${GCLOUD_FM_COLLECTOR_ID} | cut -d"_" -f1 | grep -q "fly" && echo "fly" || echo "docker")"' >> /etc/profile.d/root.sh \
    && echo 'export GCLOUD_FM_COLLECTOR_ID="$( [ -n "$FLY_MACHINE_ID" ] && echo "fly_${FLY_ID}_$FLY_MACHINE_ID" || echo "dck_${FLY_ID}_$HOSTNAME" )"' >> /etc/profile.d/root.sh \
    && echo "alias clara='$CLARA_BIN'; alias d='ls -Flap'" >> /etc/profile.d/root.sh \
    && chmod +x /etc/cron.hourly/logrotate \
    && chmod +x /etc/profile.d/*sh



# STAGE: BUILD
# Install deps only for building
FROM base AS build

RUN apt-get install -y \
        git \
        gpg \
        wget \
        build-essential \
        libcairo2-dev \
        libpango1.0-dev \
        pkg-config \
        python3 \
    && mkdir -p /etc/apt/keyrings/ \
    && wget -q -O - https://apt.grafana.com/gpg.key | gpg --dearmor | tee /etc/apt/keyrings/grafana.gpg > /dev/null \
    && echo "deb [signed-by=/etc/apt/keyrings/grafana.gpg] https://apt.grafana.com stable main" | tee /etc/apt/sources.list.d/grafana.list \
    && apt-get update \
    && apt-get install alloy \
    && pnpm i --no-optional --cache-dir .pnpm-cache -g --prod \
        node-gyp \
    && rm -rf .pnpm-cache

# Copy bd/ repo
WORKDIR $CLARA_HOME

# Run pnpm with just package info, without full source code
COPY ./.npmrc                     .
COPY ./tsconfig.json              .
COPY ./package.json               .
COPY ./pnpm-workspace.yaml        .
COPY apps/cli/src/index.ts        ./apps/cli/src/index.ts
COPY apps/cli/package.json        ./apps/cli/package.json
COPY apps/homebase/package.json   ./apps/homebase/package.json
COPY packages/config/package.json ./packages/config/package.json
COPY packages/dsl/package.json    ./packages/dsl/package.json
COPY packages/tslib/package.json  ./packages/tslib/package.json
COPY nes/package.json             ./nes/package.json

RUN PNPM_INSTALL_ARGS="--loglevel=debug" \
    && if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then \
        PNPM_INSTALL_ARGS="$PNPM_INSTALL_ARGS --unsafe-perm"; \
    fi \
    && pnpm i -r --cache-dir .pnpm-cache $PNPM_INSTALL_ARGS \
    && pnpm rb -r $PNPM_INSTALL_ARGS

# Copied in the order of least to most likely to change
COPY packages/containers/nes/config  ./config
COPY packages/containers/certs/local-pem/ ./config/certs/
COPY packages/containers/nes/scripts ./scripts
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules packages/config ./packages/config
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules packages/tslib  ./packages/tslib
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules packages/dsl    ./packages/dsl
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules apps/homebase   ./apps/homebase
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules nes ./nes

# # Set permissions, build homebase
RUN chmod +x $CLARA_HOME/apps/cli/src/index.ts \
    && chmod +x $CLARA_HOME/scripts/* \
    && cd $CLARA_HOME/apps/homebase \
    && pnpm build \
    && cd $CLARA_HOME \
    && rm -rf .pnpm-cache



# STAGE: GRAFANA ALLOY
# Copy alloy-related files from build stage
FROM base AS alloy

WORKDIR $CLARA_HOME
COPY --from=build $CLARA_HOME $CLARA_HOME
COPY --from=build /usr/bin/alloy /usr/bin/alloy
COPY --from=build /etc/alloy/ /etc/alloy/
COPY ./packages/containers/nes/config/alloy/config.alloy /etc/alloy/config.alloy
COPY --from=build /var/lib/alloy/ /var/lib/alloy/



# STAGE: DEVELOPMENT RUNTIME
# Copy from build stage and start services
FROM alloy AS development

# Start caddy, NES, frontend etc.
WORKDIR $CLARA_HOME
EXPOSE 8000 8080 8443 5173 9229
CMD ["/opt/clara/scripts/entrypoint.sh"]



# STAGE: TESTING RUNTIME
# Identical to development stage
FROM development AS testing



# STAGE: PRODUCTION RUNTIME
# Copy from build stage and start services
FROM alloy AS production

RUN apt-get clean \
    && rm -f /var/lib/apt/lists/*_*

# Copy from build stage and start services
WORKDIR $CLARA_HOME
COPY --from=build $CLARA_HOME $CLARA_HOME

# Start caddy, NES, frontend etc.
EXPOSE 8000
CMD ["/opt/clara/scripts/entrypoint.sh"]



# STAGE: STAGING RUNTIME
# Identical to production stage
FROM production AS staging

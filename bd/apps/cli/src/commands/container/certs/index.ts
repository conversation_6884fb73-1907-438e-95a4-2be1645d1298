/* eslint-disable no-sync */
import { BaseCommand } from "@core/base";
import { Flags } from "@oclif/core";
import * as fs from "fs";
import * as path from "path";

export default class CertsCommand extends BaseCommand<typeof CertsCommand> {
    static description = `Generate SSL certificates for local development using mkcert.

This command creates SSL certificates for all Clara containers in a centralized location.
Certificates are stored in packages/containers/certs/local-pem/ and can be used by all containers.`;

    static examples = [
        `$ clara container certs`,
        `$ clara container certs --domains dev.local.clararx.com,wiki.local.clararx.com`,
        `$ clara container certs --force`,
    ];

    static override flags = {
        domains: Flags.string({
            description:
                "Comma-separated list of domains to generate certificates for",
            required: false,
            default: "dev.local.clararx.com,wiki.local.clararx.com",
        }),
        force: Flags.boolean({
            description: "Force regeneration of existing certificates",
            required: false,
            default: false,
        }),
    };

    public async process(): Promise<object> {
        const { domains, force } = this.flags;

        this.app.info("Generating SSL certificates for local development...");

        try {
            // Check if mkcert is installed
            await this.checkMkcertInstalled();

            // Ensure mkcert CA is installed
            await this.ensureMkcertCA();

            // Parse domains
            const domainList = domains
                .split(",")
                .map((d) => d.trim())
                .filter((d) => d.length > 0);

            if (domainList.length === 0) {
                this.app.fatal(
                    "No domains specified for certificate generation"
                );
                return { error: "No domains specified" };
            }

            // Generate certificates
            const results = await this.generateCertificates(domainList, force);

            return {
                message: "SSL certificates generated successfully",
                domains: domainList,
                certificates: results,
            };
        } catch (error) {
            this.app.fatal(`Failed to generate certificates: ${error}`);
            return {
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    private async checkMkcertInstalled(): Promise<void> {
        try {
            const { stdout } = await this.app.$shell`which mkcert`;
            if (!stdout.trim()) {
                throw new Error("mkcert not found in PATH");
            }
            this.app.info(`Found mkcert at: ${stdout.trim()}`);
        } catch (error) {
            this.app.fatal(
                `${error}\n\nmkcert is not installed. Please install it with: brew install mkcert.`
            );
        }
    }

    private async ensureMkcertCA(): Promise<void> {
        try {
            this.app.info("Ensuring mkcert CA is installed...");
            await this.app.$shell`mkcert -install`;
            this.app.info("mkcert CA is installed and ready");
        } catch (error) {
            throw new Error(
                `Failed to install mkcert CA: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    private async generateCertificates(
        domains: string[],
        force: boolean
    ): Promise<Array<{ domain: string; status: string }>> {
        const certsDir = this.app.repoPath("packages/containers/certs/local-pem");
        const results: Array<{ domain: string; status: string }> = [];

        // Ensure certs directory exists
        if (!fs.existsSync(certsDir)) {
            fs.mkdirSync(certsDir, { recursive: true });
            this.app.info(`Created certificates directory: ${certsDir}`);
        }

        for (const domain of domains) {
            const certFile = path.join(certsDir, `${domain}.pem`);
            const keyFile = path.join(certsDir, `${domain}-key.pem`);

            // Check if certificates already exist
            if (!force && fs.existsSync(certFile) && fs.existsSync(keyFile)) {
                this.app.info(
                    `Certificates for ${domain} already exist (use --force to regenerate)`
                );
                results.push({ domain, status: "exists" });
                continue;
            }

            try {
                this.app.info(`Generating certificate for ${domain}...`);

                // Use execSync to run mkcert directly in the certs directory
                const { execSync } = await import("child_process");

                try {
                    const command = `cd "${certsDir}" && mkcert "${domain}"`;
                    const output = execSync(command, {
                        encoding: "utf8",
                        stdio: ["ignore", "pipe", "pipe"],
                    });

                    this.app.info(`mkcert output: ${output}`);

                    // Give a small delay for file system to sync
                    await new Promise((resolve) => setTimeout(resolve, 100));

                    // Verify files were created
                    if (fs.existsSync(certFile) && fs.existsSync(keyFile)) {
                        this.app.info(`✓ Certificate generated for ${domain}`);
                        results.push({ domain, status: "generated" });
                    } else {
                        throw new Error(
                            `Certificate files not found: ${certFile}, ${keyFile}`
                        );
                    }
                } catch (execError) {
                    // execSync throws on non-zero exit, but mkcert might output to stderr
                    // Check if files exist anyway
                    if (fs.existsSync(certFile) && fs.existsSync(keyFile)) {
                        this.app.info(`✓ Certificate generated for ${domain}`);
                        results.push({ domain, status: "generated" });
                    } else {
                        throw execError;
                    }
                }
            } catch (error) {
                this.app.warn(
                    `Failed to generate certificate for ${domain}: ${error}`
                );
                results.push({ domain, status: "failed" });
            }
        }

        return results;
    }
}
